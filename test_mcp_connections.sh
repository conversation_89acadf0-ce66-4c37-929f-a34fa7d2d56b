#!/bin/bash

echo "🔧 Testing MCP Connections with VPN Proxy..."
echo "=============================================="

# Source the VPN environment
source ~/.config/mcp-vpn/env.sh

echo ""
echo "📡 Testing Proxy Connection..."
echo "------------------------------"
curl -s --proxy http://127.0.0.1:9910 --connect-timeout 5 https://www.google.com > /dev/null
if [ $? -eq 0 ]; then
    echo "✅ Proxy connection working"
else
    echo "❌ Proxy connection failed"
    exit 1
fi

echo ""
echo "🗄️  Testing Supabase Connection..."
echo "-----------------------------------"
curl -s --proxy http://127.0.0.1:9910 --connect-timeout 5 https://yekarqanirdkdckimpna.supabase.co > /dev/null
if [ $? -eq 0 ]; then
    echo "✅ Supabase connection working"
else
    echo "❌ Supabase connection failed"
fi

echo ""
echo "🤖 Testing Puppeteer MCP Server..."
echo "-----------------------------------"
echo '{"jsonrpc": "2.0", "id": 1, "method": "initialize", "params": {"protocolVersion": "2024-11-05", "capabilities": {"roots": {"listChanged": true}, "sampling": {}}, "clientInfo": {"name": "test", "version": "1.0.0"}}}' | /opt/homebrew/bin/node /Users/<USER>/.npm-global/lib/node_modules/@modelcontextprotocol/server-puppeteer/dist/index.js 2>/dev/null | head -1
if [ $? -eq 0 ]; then
    echo "✅ Puppeteer MCP server responding"
else
    echo "❌ Puppeteer MCP server failed"
fi

echo ""
echo "🗄️  Testing Supabase MCP Server..."
echo "-----------------------------------"
SUPABASE_ACCESS_TOKEN="********************************************" \
SUPABASE_URL="https://yekarqanirdkdckimpna.supabase.co" \
SUPABASE_PROJECT_REF="yekarqanirdkdckimpna" \
echo '{"jsonrpc": "2.0", "id": 1, "method": "initialize", "params": {"protocolVersion": "2024-11-05", "capabilities": {"roots": {"listChanged": true}, "sampling": {}}, "clientInfo": {"name": "test", "version": "1.0.0"}}}' | /opt/homebrew/bin/node /Users/<USER>/.npm-global/lib/node_modules/@supabase/mcp-server-supabase/dist/transports/stdio.js 2>/dev/null | head -1
if [ $? -eq 0 ]; then
    echo "✅ Supabase MCP server responding"
else
    echo "❌ Supabase MCP server failed"
fi

echo ""
echo "📋 Configuration Summary:"
echo "-------------------------"
echo "MCP Config: ~/.config/mcp-vpn/mcp_config_vpn.json"
echo "Environment: ~/.config/mcp-vpn/env.sh"
echo "Proxy: http://127.0.0.1:9910"
echo ""
echo "🔄 To apply changes, restart Windsurf/Claude Desktop"
echo "=============================================="
